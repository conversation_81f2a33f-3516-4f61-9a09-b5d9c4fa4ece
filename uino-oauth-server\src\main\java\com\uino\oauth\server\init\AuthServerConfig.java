package com.uino.oauth.server.init;

import com.uino.api.client.permission.IOauthApiSvc;
import com.uino.oauth.common.service.UinoClientDetailsService;
import com.uino.oauth.common.service.UinoTokenStore;
import com.uino.oauth.common.service.UinoUserDetailsManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.crypto.password.PasswordEncoder;
// import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.OAuth2AuthorizationService;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configuration.OAuth2AuthorizationServerConfiguration;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.oauth2.server.authorization.settings.AuthorizationServerSettings;
import org.springframework.security.oauth2.server.authorization.token.DelegatingOAuth2TokenGenerator;
import org.springframework.security.oauth2.server.authorization.token.OAuth2TokenGenerator;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.beans.factory.annotation.Value;

import com.uino.oauth.server.init.handler.UinoLoginUrlAuthenticationEntryPoint;

/**
 * 授权服务器配置
 * 使用Spring Authorization Server替代旧的Spring Security OAuth2
 *
 * <AUTHOR>
 */
@Configuration
public class AuthServerConfig {

	@Autowired
	private OAuth2ScopeService oAuth2ScopeService;

	@Value("${oauth.server.issuer:}")
	private String oauthServerIssuer;

	/**
	 * 配置OAuth2授权服务器
	 */
	@Bean
	@Order(1)
	public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
		// 确保 refresh scope 已注册
		oAuth2ScopeService.registerScope("refresh");

		OAuth2AuthorizationServerConfiguration.applyDefaultSecurity(http);

		http.getConfigurer(OAuth2AuthorizationServerConfigurer.class)
			.tokenEndpoint(Customizer.withDefaults())
			.tokenIntrospectionEndpoint(Customizer.withDefaults())
			.tokenRevocationEndpoint(Customizer.withDefaults())
			.oidc(Customizer.withDefaults());	// 启用OpenID Connect 1.0

		http
			// 当未经身份验证的用户尝试访问受保护的资源时，重定向到登录页面
			.exceptionHandling(exceptions ->
				exceptions.authenticationEntryPoint(
					new UinoLoginUrlAuthenticationEntryPoint("/oauth/login")
				)
			)
			// 移除JWT认证配置
			// 使用基于表单的认证
			.formLogin(Customizer.withDefaults());

		return http.build();
	}



	/**
	 * 配置授权服务器设置
	 */
	@Bean
	public AuthorizationServerSettings authorizationServerSettings() {
		AuthorizationServerSettings.Builder builder = AuthorizationServerSettings.builder();

		// 如果配置了issuer，则使用配置的值；否则使用默认配置
		if (oauthServerIssuer != null && !oauthServerIssuer.trim().isEmpty()) {
			builder.issuer(oauthServerIssuer.trim());
		}

		return builder.build();
	}

	/**
	 * 自定义Token生成器
	 *
	 * @return OAuth2TokenGenerator
	 */
	@Bean
	public OAuth2TokenGenerator<?> tokenGenerator(IOauthApiSvc oauthApiSvc) {
		UUIDOAuth2TokenGenerator uuidoAuth2TokenGenerator = new UUIDOAuth2TokenGenerator(oauthApiSvc);
		UUIDOAuth2RefreshTokenGenerator refreshTokenGenerator = new UUIDOAuth2RefreshTokenGenerator();
		return new DelegatingOAuth2TokenGenerator(uuidoAuth2TokenGenerator, refreshTokenGenerator);
	}

}
