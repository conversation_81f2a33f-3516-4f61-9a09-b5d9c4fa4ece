package com.uino.oauth.server.init.handler;

import java.io.IOException;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.DefaultRedirectStrategy;
import org.springframework.security.web.RedirectStrategy;
import org.springframework.security.web.util.UrlUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 自定义登录URL认证入口点，确保重定向URL包含端口号
 * 
 * @author: system
 * @create: 2024/01/01
 */
@Slf4j
public class UinoLoginUrlAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final String loginFormUrl;
    private final RedirectStrategy redirectStrategy = new DefaultRedirectStrategy();
    private boolean forceHttps = false;
    private boolean useForwardedHeaders = false;

    public UinoLoginUrlAuthenticationEntryPoint(String loginFormUrl) {
        if (!UrlUtils.isValidRedirectUrl(loginFormUrl)) {
            throw new IllegalArgumentException("loginFormUrl must be a valid redirect URL");
        }
        this.loginFormUrl = loginFormUrl;
    }

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
            AuthenticationException authException) throws IOException, ServletException {
        
        String redirectUrl = buildRedirectUrlToLoginPage(request);
        log.debug("重定向到登录页面: {}", redirectUrl);
        
        redirectStrategy.sendRedirect(request, response, redirectUrl);
    }

    /**
     * 构建包含完整服务器信息（包括端口号）的重定向URL
     */
    protected String buildRedirectUrlToLoginPage(HttpServletRequest request) {
        String scheme = getScheme(request);
        String serverName = getServerName(request);
        int serverPort = getServerPort(request);
        String contextPath = request.getContextPath();
        
        StringBuilder url = new StringBuilder();
        url.append(scheme).append("://").append(serverName);
        
        // 只有在非标准端口时才添加端口号
        if (needsPortInUrl(scheme, serverPort)) {
            url.append(":").append(serverPort);
        }
        
        url.append(contextPath);
        
        // 确保loginFormUrl以/开头
        if (!loginFormUrl.startsWith("/")) {
            url.append("/");
        }
        url.append(loginFormUrl);
        
        return url.toString();
    }

    /**
     * 获取请求的协议方案
     */
    private String getScheme(HttpServletRequest request) {
        if (forceHttps) {
            return "https";
        }
        
        if (useForwardedHeaders) {
            String forwardedProto = request.getHeader("X-Forwarded-Proto");
            if (forwardedProto != null) {
                return forwardedProto;
            }
        }
        
        return request.getScheme();
    }

    /**
     * 获取服务器名称
     */
    private String getServerName(HttpServletRequest request) {
        if (useForwardedHeaders) {
            String forwardedHost = request.getHeader("X-Forwarded-Host");
            if (forwardedHost != null) {
                // 如果X-Forwarded-Host包含端口号，需要分离
                int colonIndex = forwardedHost.indexOf(':');
                if (colonIndex > 0) {
                    return forwardedHost.substring(0, colonIndex);
                }
                return forwardedHost;
            }
        }
        
        return request.getServerName();
    }

    /**
     * 获取服务器端口
     */
    private int getServerPort(HttpServletRequest request) {
        if (useForwardedHeaders) {
            String forwardedHost = request.getHeader("X-Forwarded-Host");
            if (forwardedHost != null && forwardedHost.contains(":")) {
                try {
                    int colonIndex = forwardedHost.indexOf(':');
                    return Integer.parseInt(forwardedHost.substring(colonIndex + 1));
                } catch (NumberFormatException e) {
                    log.warn("无法解析X-Forwarded-Host中的端口号: {}", forwardedHost);
                }
            }
            
            String forwardedPort = request.getHeader("X-Forwarded-Port");
            if (forwardedPort != null) {
                try {
                    return Integer.parseInt(forwardedPort);
                } catch (NumberFormatException e) {
                    log.warn("无法解析X-Forwarded-Port: {}", forwardedPort);
                }
            }
        }
        
        return request.getServerPort();
    }

    /**
     * 判断是否需要在URL中包含端口号
     */
    private boolean needsPortInUrl(String scheme, int port) {
        return !("http".equals(scheme) && port == 80) && 
               !("https".equals(scheme) && port == 443);
    }

    /**
     * 设置是否强制使用HTTPS
     */
    public void setForceHttps(boolean forceHttps) {
        this.forceHttps = forceHttps;
    }

    /**
     * 设置是否使用转发头信息（用于代理环境）
     */
    public void setUseForwardedHeaders(boolean useForwardedHeaders) {
        this.useForwardedHeaders = useForwardedHeaders;
    }
}
