#\u6388\u6743\u670D\u52A1\u7AEF\u53E3
spring.main.allow-circular-references=true
server.port=9080
server.servlet.context-path=/oauth
spring.application.name=oauth
oauth.open=true
#elasticsearch\u76F8\u5173\u914D\u7F6E
esIps=*************:9200
isAuth=true
esUser=uinnova
esPwd=Uinnova@123

login.success.url=http://localhost/tarsier-eam/redirectAuth?goPageUrl=http://localhost/eam-web/index.html%23/
logout.sucess.url=http://localhost/tarsier-eam/redirectAuth?goPageUrl=http://localhost/eam-web/index.html%23/
login.page.url=http://localhost/eam-web/login.html
permission.http.prefix=http://localhost:1536/tarsier-comm
#\u672C\u5730\u8D44\u6E90http\u670D\u52A1\u5730\u5740
http.resource.space=http://localhost
#\u672C\u5730\u8D44\u6E90\u5B58\u50A8\u5730\u5740
local.resource.space = /usr/local/uino_data

#\u57FA\u7840\u6A21\u5757\u670D\u52A1\u52A0\u8F7D\u65B9\u5F0F\uFF0C\u652F\u6301local/rpc
base.load-type=local
eureka.client.enabled=false

uino.monitor.ep.exist=
uino.monitor.event.send.url=

server.compression.enabled=true
server.compression.mime-types=text/plain,application/x-javascript,text/css,application/xml,application/javascript,text/javascript,application/x-httpd-php,image/jpeg,image/gif,image/png,image/svg,image/x-ms-bmp,application/octet-stream,application/json
server.compression.min-response-size=100

oauth.client.id=tarsier-comm
spring.main.allow-bean-definition-overriding=true

logging.level.org.springframework.security=DEBUG

# \u5BF9\u8C61\u5B58\u50A8\u7684\u7EC8\u7AEF\u8282\u70B9\u5730\u5740
spring.cloud.huawei.obs.endpoint=obs.cn-north-4.myhuaweicloud.com
# \u5BF9\u8C61\u5B58\u50A8\u8BBF\u95EEak
spring.cloud.huawei.obs.access-key=AAAABBBBB
# \u5BF9\u8C61\u5B58\u50A8\u8BBF\u95EE sk
spring.cloud.huawei.obs.secret-key=HiZQUzY
# \u5BF9\u8C61\u5B58\u50A8\u6876\u540D\u79F0
spring.cloud.huawei.obs.bucketName=quickea
# \u5BF9\u8C61\u5B58\u50A8\u8D44\u6E90\u8BBF\u95EE\u94FE\u63A5\u7684\u7B7E\u540D\u6709\u6548\u671F\uFF08s\uFF09
spring.cloud.huawei.obs.urlExpireSeconds=3600
# \u5B9E\u73B0\u5BF9\u8C61\u5B58\u50A8\u7684\u64CD\u4F5C\u7684SDK\uFF08huawei\uFF1A\u534E\u4E3A\u5B98\u65B9\u63D0\u4F9B\u7684sdk\uFF0Cxinwang\uFF1A\u65B0\u7F51\u63D0\u4F9B\u7684sdk\uFF09\uFF08\u65B0\u589E\u914D\u7F6E\uFF09
rsm.util.sdkType=huawei
# \u5F53\u524D\u73AF\u5883\u662F\u5426\u4F7F\u7528\u5BF9\u8C61\u5B58\u50A8
obs.use=false
# \u8D44\u6E90\u8DEF\u5F84\u524D\u7F00\uFF08\u540E\u53F0\u90E8\u5206\u529F\u80FD\uFF0C\u5904\u7406\u8D44\u6E90\u7684\u8DEF\u5F84\u65F6\uFF0C\u5E76\u672A\u4F7F\u7528http.resource.space\u914D\u7F6E\uFF0C\u800C\u662F\u5728\u8DEF\u5F84\u524D\u62FC\u63A5\u4E86\u201C/rsm\u201D\uFF0C\u5BF9\u4E8E\u8FD9\u90E8\u5206\u529F\u80FD\u9700\u8981\u4F7F\u7528\u8BE5\u914D\u7F6E\u5904\u7406\u8FD4\u56DE\u7ED9\u524D\u53F0\u7684\u8D44\u6E90\u8DEF\u5F84\uFF09\u3002\uFF08\u65B0\u589E\u914D\u7F6E\uFF0C\u975E\u5FC5\u9700\u3002\u4E0D\u914D\u7F6E\u65F6\u9ED8\u8BA4\u503C\u4E3A\uFF1A/tarsier-eam/rsm\uFF09
obs.rsm.url.prefix=/tarsier-eam/rsm

#redise
# Redis\u670D\u52A1\u5668\u5730\u5740
spring.data.redis.host=**************
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u7AEF\u53E3
spring.data.redis.port=6379
#spring.data.redis.cluster.nodes=*************:6379,*************:6380,**************:6379,**************:6380,**************:6379,**************:6380
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.data.redis.database=0
# Redis\u670D\u52A1\u5668\u8FDE\u63A5\u5BC6\u7801\uFF08\u9ED8\u8BA4\u4E3A\u7A7A\uFF09
spring.data.redis.password=Uinnova@123
# \u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4\uFF08\u6BEB\u79D2\uFF09
spring.data.redis.timeout=10000
# Lettuce
# \u8FDE\u63A5\u6C60\u6700\u5927\u8FDE\u63A5\u6570\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.data.redis.lettuce.pool.max-active=8
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.data.redis.lettuce.pool.max-wait=10000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.data.redis.lettuce.pool.max-idle=8
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.data.redis.lettuce.pool.min-idle=0

# \u662F\u5426\u5F00\u542F\u914D\u7F6E\u4E2D\u5FC3
spring.cloud.nacos.config.enabled=false
# \u662F\u5426\u5F00\u542F\u670D\u52A1\u6CE8\u518C\u8C03\u7528
spring.cloud.nacos.discovery.enabled=false
# \u662F\u5426\u5F00\u542F\u6D41\u91CF\u76D1\u63A7
spring.cloud.sentinel.enabled=false