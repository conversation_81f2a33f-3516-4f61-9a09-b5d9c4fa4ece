#授权服务端口
server.port=9080
server.servlet.context-path=/${CONTEXTPATH}
spring.application.name=oauth
oauth.open=true
# OAuth服务器issuer配置，确保重定向URL包含正确的端口号
oauth.server.issuer=${OAUTH_SERVER_ISSUER}
#elasticsearch相关配置
esIps=${ELASTIC_HOST}
isAuth=false
esUser=admin
esPwd=admin

login.success.url=${LOGIN_URL}
logout.sucess.url=${LOGOUT_URL}
login.page.url=${LOGINPAGE_URL}
#权限模块http server path
permission.http.prefix=${PERMISSION_PATH}
#本地资源http服务地址
http.resource.space=${HTTP_RESOURCE}
#本地资源存储地址
local.resource.space = /usr/local/uino_data

#基础模块服务加载方式，支持local/rpc
base.load-type=local
eureka.client.enabled=false

server.compression.enabled=true
server.compression.mime-types=text/plain,application/x-javascript,text/css,application/xml,application/javascript,text/javascript,application/x-httpd-php,image/jpeg,image/gif,image/png,image/svg,image/x-ms-bmp,application/octet-stream,application/json
server.compression.min-response-size=100

uino.monitor.ep.exist=
uino.monitor.event.send.url=







logging.level.org.springframework.security: DEBUG
